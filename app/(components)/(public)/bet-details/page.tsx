import React from 'react';

// Simple inline types to avoid import issues
interface BetDetailsData {
  provider: string;
  marketDetail: {
    marketId: string;
    marketName: string;
    marketStatus: string;
  };
  betDetails: {
    betId: string;
    settlementStatus: string;
    betAmount: number;
    settlementAmount: number;
    createdDate: string;
    payoutStatus: number;
    status: string;
    betQrCode: string;
  };
  betList: Array<{
    betId: string;
    marketName: string;
    rate: number;
    stake: number;
  }>;
  customerSupportDetails: {
    id: string;
    phone: string;
    email: string;
  };
}

interface ApiResponse {
  code: number;
  message: string;
  success: number;
  data: BetDetailsData;
}

/**
 * Public Bet Details Page
 *
 * A public page that displays bet details without requiring authentication.
 * Accessible via URL: /bet-details?bet_id=<transaction_id>
 */
const BetDetailsPage: React.FC = () => {

  // Sample data for testing
  const data = {
    marketDetail: {
      marketId: "684089b33e17c3e44a69cc43",
      marketName: "Cricket -> Twenty20. Mumbai League -> <PERSON><PERSON>heri vs Multan Tigers -> Winner. With overtime",
      marketStatus: "SettledMarket"
    },
    betDetails: {
      betId: "31af4279-35e2-4c31-bb0d-ffa95ddd30a9",
      status: "Win",
      betAmount: 5070,
      settlementAmount: 9531.6
    }
  };

  return (
    <div className="min-h-screen bg-bodybg text-white">
      {/* Logo Section */}
      <div className="flex justify-end p-6">
        <div className="w-[100px] h-[100px] bg-gray-600 flex items-center justify-center">
          LOGO
        </div>
      </div>

      {/* Global Header */}
      <div className="relative rounded-lg mb-6 overflow-hidden mx-6 h-[80px] bg-purple-header-gradient">
        <div className="absolute left-[-5px] top-1/2 transform -translate-y-1/2">
          <div className="w-[80px] h-[80px] bg-gray-500 rotate-[8deg] flex items-center justify-center text-xs">
            ICON
          </div>
        </div>
        <div className="relative z-10 flex items-center justify-between h-full px-6">
          <div className="flex-1 pl-16">
            <h1 className="text-white text-2xl font-medium">Print Betslip</h1>
          </div>
        </div>
      </div>

      {/* Bet Details Content */}
      <div className="max-w-4xl mx-auto px-6 pb-8">
        {/* Market Details Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Market Details
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Market ID:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.marketDetail.marketId}</span>
            </div>
            <div className="flex justify-between items-start">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Market Name:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white text-right max-w-[60%]">{data.marketDetail.marketName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Market Status:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.marketDetail.marketStatus}</span>
            </div>
          </div>
        </div>

        {/* Bet Details Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Bet Details
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Bet ID:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.betDetails.betId}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Status:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.betDetails.status}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Bet Amount:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">${(data.betDetails.betAmount / 100).toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BetDetailsPage;
