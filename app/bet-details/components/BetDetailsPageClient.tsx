'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import BetDetailsUI from '@/shared/components/public/BetDetailsUI';
import { fetchPublicBetDetails, PublicBetDetailsResponse } from '@/shared/services/publicBetDetailsService';

/**
 * Client-side component for the bet details page
 * Handles all the interactive functionality and API calls
 */
const BetDetailsPageClient: React.FC = () => {
  const searchParams = useSearchParams();
  const betId = searchParams.get('bet_id');
  
  const [betData, setBetData] = useState<PublicBetDetailsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBetDetails = async () => {
      if (!betId) {
        setError('Bet ID is required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        const data = await fetchPublicBetDetails(betId);
        setBetData(data);
      } catch (err) {
        console.error('Error loading bet details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load bet details');
      } finally {
        setIsLoading(false);
      }
    };

    loadBetDetails();
  }, [betId]);

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">Error Loading Bet Details</div>
          <div className="text-white text-lg mb-6">{error}</div>
          {!betId && (
            <div className="text-gray-400 text-sm">
              Please provide a valid bet_id parameter in the URL
            </div>
          )}
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-golden mx-auto mb-4"></div>
          <div className="text-white text-xl">Loading bet details...</div>
        </div>
      </div>
    );
  }

  // Success state
  if (betData) {
    return <BetDetailsUI betData={betData} isLoading={false} />;
  }

  // Fallback state
  return (
    <div className="min-h-screen bg-bodybg flex items-center justify-center">
      <div className="text-white text-xl">No bet details available</div>
    </div>
  );
};

export default BetDetailsPageClient;
