// app/bet-details/layout.tsx
import React from 'react';
import type { Metadata } from 'next';

// Generate metadata for the bet details page
export const metadata: Metadata = {
  title: 'Bet Details - Print Betslip',
  description: 'View detailed information about your bet slip',
  robots: 'noindex, nofollow', // Since this is a private bet details page
};

interface BetDetailsLayoutProps {
  children: React.ReactNode;
}

/**
 * Layout for the public bet details page
 * This layout provides a minimal structure without authentication or navigation
 */
export default function BetDetailsLayout({ children }: BetDetailsLayoutProps) {
  return (
    <div className="bet-details-layout">
      {children}
    </div>
  );
}
