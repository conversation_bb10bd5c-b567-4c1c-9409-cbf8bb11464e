import React from 'react';
import BetDetailsPageClient from './components/BetDetailsPageClient';

/**
 * Public Bet Details Page (Server Component)
 *
 * A public page that displays bet details without requiring authentication.
 * Accessible via URL: /bet-details?bet_id=<transaction_id>
 *
 * Features:
 * - No authentication required
 * - No header, sidebar, or navigation
 * - Dark theme styling
 * - Logo display (100x100px top right)
 * - Global header with "Print Betslip" title
 * - Bet details display matching PrintBetSlipUI structure
 */
const BetDetailsPage: React.FC = () => {
  return <BetDetailsPageClient />;
};

export default BetDetailsPage;
