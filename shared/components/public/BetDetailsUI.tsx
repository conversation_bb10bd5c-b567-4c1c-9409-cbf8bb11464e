'use client';

import React from 'react';
import Image from 'next/image';
import { PublicBetDetailsResponse, formatCurrency, formatDate, getStatusColor, getPayoutStatusText } from '@/shared/services/publicBetDetailsService';

interface BetDetailsUIProps {
  betData: PublicBetDetailsResponse;
  isLoading?: boolean;
}

/**
 * Public Bet Details UI Component
 * 
 * A standalone component for displaying bet details without authentication
 * Features dark theme styling with specific typography requirements:
 * - Section headings: Rubik, 700 weight, 24px, #616161 color
 * - Labels/values: Rubik, 500 weight, 18px, white color
 */
const BetDetailsUI: React.FC<BetDetailsUIProps> = ({ betData, isLoading = false }) => {
  if (isLoading) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-white text-xl">Loading bet details...</div>
      </div>
    );
  }

  const { data } = betData;

  return (
    <div className="min-h-screen bg-bodybg text-white">
      {/* Logo Section */}
      <div className="flex justify-end p-6">
        <div className="w-[100px] h-[100px] relative">
          <Image
            src="/assets/images/header-logo.png"
            alt="Logo"
            fill
            className="object-contain"
            priority
          />
        </div>
      </div>

      {/* Global Header */}
      <div className="relative rounded-lg mb-6 overflow-hidden mx-6 h-[80px] bg-purple-header-gradient">
        {/* Header Icon on the left side */}
        <div className="absolute left-[-5px] top-1/2 transform -translate-y-1/2">
          <Image
            src="/assets/images/header-icon.png"
            alt="header icon"
            className="w-[80px] h-[80px] rotate-[8deg]"
            width={80}
            height={80}
          />
        </div>

        {/* Content */}
        <div className="relative z-10 flex items-center justify-between h-full px-6">
          <div className="flex-1 pl-16">
            {/* Page Title */}
            <h1 className="text-white text-2xl font-medium">
              Print Betslip
            </h1>
          </div>
        </div>
      </div>

      {/* Bet Details Content */}
      <div className="max-w-4xl mx-auto px-6 pb-8">
        {/* Market Details Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Market Details
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Market ID:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.marketDetail.marketId}</span>
            </div>
            <div className="flex justify-between items-start">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Market Name:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white text-right max-w-[60%]">{data.marketDetail.marketName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Market Status:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.marketDetail.marketStatus}</span>
            </div>
          </div>
        </div>

        {/* Bet Details Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Bet Details
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Bet ID:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.betDetails.betId}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Settlement Status:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.betDetails.settlementStatus}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Bet Amount:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{formatCurrency(data.betDetails.betAmount / 100)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Settlement Amount:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{formatCurrency(data.betDetails.settlementAmount / 100)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Created Date:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{formatDate(data.betDetails.createdDate)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Status:</span>
              <span className={`font-rubik font-medium text-lg leading-none capitalize ${getStatusColor(data.betDetails.status)}`}>{data.betDetails.status}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Payout Status:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{getPayoutStatusText(data.betDetails.payoutStatus)}</span>
            </div>
          </div>
        </div>

        {/* Individual Bets Section */}
        {data.betList && data.betList.length > 0 && (
          <div className="mb-8">
            <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
              Individual Bets
            </h2>
            <div className="space-y-6">
              {data.betList.map((bet, index) => (
                <div key={index} className="bg-surface p-4 rounded-lg">
                  <h3 className="font-rubik font-medium text-lg leading-none capitalize text-white mb-3">Bet {index + 1}</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Bet ID:</span>
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{bet.betId}</span>
                    </div>
                    <div className="flex justify-between items-start">
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Market Name:</span>
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white text-right max-w-[60%]">{bet.marketName}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Rate:</span>
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{bet.rate}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Stake:</span>
                      <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{formatCurrency(bet.stake / 100)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Customer Support Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Customer Support
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Support ID:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.customerSupportDetails.id}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Phone:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.customerSupportDetails.phone}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">Email:</span>
              <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{data.customerSupportDetails.email}</span>
            </div>
          </div>
        </div>

        {/* QR Code Section */}
        {data.betDetails.betQrCode && (
          <div className="text-center">
            <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
              QR Code
            </h2>
            <div className="inline-block p-4 bg-white rounded-lg">
              <div className="w-32 h-32 bg-gray-200 flex items-center justify-center text-black text-sm">
                QR Code
              </div>
            </div>
            <p className="font-rubik font-medium text-lg leading-none capitalize text-white mt-2">
              Scan for bet verification
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BetDetailsUI;
